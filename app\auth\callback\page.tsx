// app/auth/callback/page.tsx
import { Suspense } from 'react';
import AuthCallbackHandler from './auth-callback-handler';
import { Loader2 } from 'lucide-react';

// Add a loading fallback component
function AuthCallbackLoading() {
  return (
    <div className="flex min-h-screen flex-col items-center justify-center bg-gray-50">
      <div className="text-center">
        <Loader2 className="h-12 w-12 animate-spin text-primary mx-auto mb-4" />
        <h2 className="text-2xl font-bold mb-4">Loading authentication...</h2>
        <p className="text-gray-600">Please wait.</p>
      </div>
    </div>
  );
}

export default function AuthCallbackPage() {
  return (
    <Suspense fallback={<AuthCallbackLoading />}>
      <AuthCallbackHandler />
    </Suspense>
  );
}