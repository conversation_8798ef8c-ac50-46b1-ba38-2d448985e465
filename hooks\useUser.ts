'use client';

import { useState, useEffect } from 'react';
import { getUser } from '@/utils/supabase/queries';
import { createClient } from '@/utils/supabase/client';

export function useUser() {
  const supabase = createClient();
  const [user, setUser] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [initialized, setInitialized] = useState(false);

  useEffect(() => {
    // Set a variable to track if this effect is still valid
    let isMounted = true;

    async function loadUser() {
      if (!isMounted) return;

      // Keep loading state true until we finish
      setLoading(true);

      try {
        const userData = await getUser(supabase);

        if (!isMounted) return;

        setUser(userData);
        setInitialized(true);
      } catch (error) {
        if (!isMounted) return;

        console.error('Error loading user:', error);
        setUser(null);
        setInitialized(true);
      } finally {
        if (isMounted) {
          setLoading(false);
        }
      }
    }

    loadUser();

    // Cleanup function to prevent setting state on unmounted component
    return () => {
      isMounted = false;
    };
  }, []);

  return { user, loading, initialized };
}
