'use client';

import { useState, useEffect } from 'react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { useToast } from '@/components/ui/use-toast';
import { useRouter, useSearchParams } from 'next/navigation';
import { Loader2 } from 'lucide-react';
import { sendMigrationPasswordReset } from '@/utils/supabase/auth';

export default function MigratePassword() {
  const { toast } = useToast();
  const router = useRouter();
  const searchParams = useSearchParams();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [email, setEmail] = useState<string | null>(null);
  const [resetEmailSent, setResetEmailSent] = useState(false);

  useEffect(() => {
    // Get email from URL parameters
    const emailParam = searchParams.get('email');
    if (!emailParam) {
      toast({
        title: 'Error',
        description: 'Email parameter is missing from URL',
        variant: 'destructive'
      });
      router.push('/signin');
      return;
    }
    setEmail(emailParam);
  }, [searchParams, router, toast]);

  const handlePasswordReset = async () => {
    if (isSubmitting || !email || resetEmailSent) return;
    setIsSubmitting(true);

    try {
      const result = await sendMigrationPasswordReset(email);

      if (result.type === 'success') {
        setResetEmailSent(true);
        toast({
          title: result.title,
          description: result.description,
          variant: 'default',
          duration: 5000
        });

        // Wait for 5 seconds before redirecting
        setTimeout(() => {
          router.push(result.redirectPath || '/signin');
        }, 5000);
      } else {
        throw new Error(result.description);
      }
    } catch (error) {
      console.error('Password reset error:', error);
      toast({
        title: 'Error Sending Reset Email',
        description:
          error instanceof Error
            ? error.message
            : 'An unexpected error occurred',
        variant: 'destructive',
        duration: 5000
      });
      setResetEmailSent(false);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="flex min-h-screen items-center justify-center bg-gray-100">
      <div className="max-w-md w-full bg-white p-8 rounded-lg shadow-md">
        <h1 className="text-2xl font-bold mb-6 text-gray-900">
          Reset Your Password
        </h1>
        <div className="space-y-6">
          <div className="bg-blue-50 p-4 rounded-md">
            <p className="text-sm text-blue-700">
              To set up your password, we'll send you a password reset email.
              Click the link in the email to set your new password.
            </p>
          </div>

          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Email
              </label>
              <Input
                type="email"
                value={email || ''}
                disabled
                className="bg-gray-50 border-gray-300"
              />
            </div>

            <Button
              onClick={handlePasswordReset}
              className="w-full bg-[#118073] hover:bg-[#16a38a] text-white transition-colors flex items-center justify-center gap-2"
              disabled={isSubmitting || !email || resetEmailSent}
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin" />
                  <span>Sending Reset Email...</span>
                </>
              ) : resetEmailSent ? (
                'Reset Email Sent!'
              ) : (
                'Send Password Reset Email'
              )}
            </Button>

            {resetEmailSent && (
              <p className="text-sm text-gray-600 text-center">
                Redirecting to sign-in page in a few seconds...
              </p>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
