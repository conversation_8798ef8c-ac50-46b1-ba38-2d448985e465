'use client';

import { useEffect, useState } from 'react';
import {
  getDashboardMetrics,
  getRecentActivity,
  type DashboardMetrics,
  type RecentActivity
} from '@/actions/admin/dashboard';
import {
  MetricCards,
  RecentActivityCard
} from '@/components/admin/charts';
import { Button } from '@/components/ui/button';
import { RefreshCw } from 'lucide-react';

interface DashboardState {
  metrics: DashboardMetrics | null;
  recentActivity: RecentActivity[];
  isLoading: boolean;
  error: string | null;
}

export default function AdminPage() {
  const [dashboardState, setDashboardState] = useState<DashboardState>({
    metrics: null,
    recentActivity: [],
    isLoading: true,
    error: null
  });

  const fetchDashboardData = async () => {
    setDashboardState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      // Only 2 database calls total - OPTIMIZED!
      const [metrics, recentActivity] = await Promise.all([
        getDashboardMetrics(), // 7 queries
        getRecentActivity(5)    // 1 query
      ]);

      setDashboardState({
        metrics,
        recentActivity,
        isLoading: false,
        error: null
      });
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
      setDashboardState(prev => ({
        ...prev,
        isLoading: false,
        error: 'Failed to load dashboard data. Please try again.'
      }));
    }
  };

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const handleRefresh = () => {
    fetchDashboardData();
  };

  if (dashboardState.error) {
    return (
      <div className="container mx-auto p-6">
        <div className="text-center py-12">
          <h1 className="text-2xl font-bold text-red-600 mb-4">Error Loading Dashboard</h1>
          <p className="text-muted-foreground mb-4">{dashboardState.error}</p>
          <Button onClick={handleRefresh}>
            <RefreshCw className="mr-2 h-4 w-4" />
            Try Again
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Admin Dashboard</h1>
          <p className="text-muted-foreground">
            Overview of platform metrics and recent activity
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            onClick={handleRefresh}
            disabled={dashboardState.isLoading}
          >
            <RefreshCw className={`mr-2 h-4 w-4 ${dashboardState.isLoading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>
      </div>

      {/* Main Dashboard Content */}
      <div className="grid gap-6">
        {/* Metrics Cards */}
        {dashboardState.metrics && (
          <MetricCards metrics={dashboardState.metrics} />
        )}

        {/* Recent Activity */}
        <div className="grid gap-6 lg:grid-cols-1">
          <RecentActivityCard activities={dashboardState.recentActivity} />
        </div>
      </div>
    </div>
  );
}
