'use client';

import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Calendar, RefreshCw } from 'lucide-react';

export type TimePeriod = '7d' | '30d' | '90d' | '1y';

interface TimePeriodSelectorProps {
  selectedPeriod: TimePeriod;
  onPeriodChange: (period: TimePeriod) => void;
  onRefresh?: () => void;
  isLoading?: boolean;
}

const periodOptions = [
  { value: '7d' as TimePeriod, label: 'Last 7 days' },
  { value: '30d' as TimePeriod, label: 'Last 30 days' },
  { value: '90d' as TimePeriod, label: 'Last 90 days' },
  { value: '1y' as TimePeriod, label: 'Last year' },
];

export function TimePeriodSelector({ 
  selectedPeriod, 
  onPeriodChange, 
  onRefresh,
  isLoading = false 
}: TimePeriodSelectorProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Calendar className="h-5 w-5" />
            <span>Time Period</span>
          </div>
          {onRefresh && (
            <Button
              variant="outline"
              size="sm"
              onClick={onRefresh}
              disabled={isLoading}
              className="h-8 w-8 p-0"
            >
              <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
            </Button>
          )}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <Select value={selectedPeriod} onValueChange={onPeriodChange}>
            <SelectTrigger>
              <SelectValue placeholder="Select time period" />
            </SelectTrigger>
            <SelectContent>
              {periodOptions.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          
          <div className="grid grid-cols-2 gap-2">
            {periodOptions.map((option) => (
              <Button
                key={option.value}
                variant={selectedPeriod === option.value ? "default" : "outline"}
                size="sm"
                onClick={() => onPeriodChange(option.value)}
                className="text-xs"
              >
                {option.label.replace('Last ', '')}
              </Button>
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

// Utility function to get days from period
export function getDaysFromPeriod(period: TimePeriod): number {
  switch (period) {
    case '7d':
      return 7;
    case '30d':
      return 30;
    case '90d':
      return 90;
    case '1y':
      return 365;
    default:
      return 30;
  }
}

// Utility function to format period for display
export function formatPeriodLabel(period: TimePeriod): string {
  const option = periodOptions.find(opt => opt.value === period);
  return option?.label || 'Last 30 days';
}
