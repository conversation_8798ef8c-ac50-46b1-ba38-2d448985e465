'use server';

import { createClient } from '@/utils/supabase/server';

export interface DashboardMetrics {
  totalJobs: number;
  totalApplications: number;
  totalCompanies: number;
  totalUsers: number;
  startupJobs: number;
  publicJobs: number;
  startupApplications: number;
  publicApplications: number;
  startupCompanies: number;
  publicCompanies: number;
}

export interface RecentActivity {
  id: string;
  type: 'job' | 'application' | 'company';
  title: string;
  description: string;
  timestamp: string;
  status?: string;
}

/**
 * Get essential dashboard metrics - OPTIMIZED with single query batch
 */
export async function getDashboardMetrics(): Promise<DashboardMetrics> {
  const supabase = createClient();

  // Single batch of parallel queries - only 7 requests total
  const [
    { count: startupJobs },
    { count: publicJobs },
    { count: startupApplications },
    { count: publicApplications },
    { count: startupCompanies },
    { count: publicCompanies },
    { count: totalUsers }
  ] = await Promise.all([
    supabase.from('startup_jobs').select('*', { count: 'exact' }),
    supabase.from('public_firm_jobs').select('*', { count: 'exact' }),
    supabase.from('startup_applications').select('*', { count: 'exact' }),
    supabase.from('public_firm_applications').select('*', { count: 'exact' }),
    supabase.from('startup_companies').select('*', { count: 'exact' }),
    supabase.from('public_firm_companies').select('*', { count: 'exact' }),
    supabase.from('users').select('*', { count: 'exact' })
  ]);

  // Calculate totals
  const totalJobs = (startupJobs || 0) + (publicJobs || 0);
  const totalApplications = (startupApplications || 0) + (publicApplications || 0);
  const totalCompanies = (startupCompanies || 0) + (publicCompanies || 0);

  return {
    totalJobs,
    totalApplications,
    totalCompanies,
    totalUsers: totalUsers || 0,
    startupJobs: startupJobs || 0,
    publicJobs: publicJobs || 0,
    startupApplications: startupApplications || 0,
    publicApplications: publicApplications || 0,
    startupCompanies: startupCompanies || 0,
    publicCompanies: publicCompanies || 0
  };
}

/**
 * Get recent activity - OPTIMIZED with single query
 */
export async function getRecentActivity(limit: number = 5): Promise<RecentActivity[]> {
  const supabase = createClient();

  // Only get recent jobs - single query instead of multiple
  const { data: recentJobs } = await supabase
    .from('startup_jobs')
    .select('id, title, created_at, status')
    .order('created_at', { ascending: false })
    .limit(limit);

  return (recentJobs || []).map(job => ({
    id: job.id,
    type: 'job' as const,
    title: job.title,
    description: 'New startup job posted',
    timestamp: job.created_at,
    status: job.status
  }));
}
