import { getURL } from '@/lib/utils';
import { createClient } from '@/utils/supabase/client';
import { Provider, AuthError } from '@supabase/supabase-js';

// Define consistent response type
export type AuthResponse = {
  type: 'success' | 'error' | 'info';
  title: string;
  description: string;
  redirectPath?: string;
  data?: any;
};

/**
 * Sign up with email and password
 * @param formData Form data containing email, password and optional user data
 * @returns AuthResponse with result of the operation
 */
export async function signUp(formData: FormData): Promise<AuthResponse> {
  try {
    // Input validation
    if (!formData) {
      return {
        type: 'error',
        title: 'Sign Up Failed',
        description: 'Missing form data'
      };
    }

    const email = String(formData.get('email') || '').trim();
    const password = String(formData.get('password') || '').trim();
    const userType = String(formData.get('userType') || '').trim();

    // Validate required fields
    if (!email || !password) {
      return {
        type: 'error',
        title: 'Sign Up Failed',
        description: 'Email and password are required'
      };
    }

    if (!userType) {
      return {
        type: 'error',
        title: 'No User Type Selected',
        description: 'Please select a User type'
      };
    }

    const supabase = createClient();

    // Sign up the user with Supabase
    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        emailRedirectTo: `${getURL()}/auth/callback?next=/dashboard`,
        data: {
          user_type: userType,
          // Any additional metadata can be included here
          startup_company: formData.get('company')
            ? String(formData.get('company')).trim()
            : null
        }
      }
    });

    if (error) {
      throw error;
    }

    // Check if email confirmation is required
    if (data?.user && !data.user.email_confirmed_at) {
      return {
        type: 'info',
        title: 'Verification Required',
        description: 'Please check your email for the verification link.',
        data: { user: data.user }
      };
    }

    // If the user has already verified their email (unlikely for new signups)
    return {
      type: 'success',
      title: 'Sign Up Successful',
      description: 'You have been successfully signed up.',
      redirectPath: '/dashboard',
      data: { user: data?.user }
    };
  } catch (error) {
    const authError = error as AuthError;
    let description = 'An unexpected error occurred';

    // Provide user-friendly error messages based on error type
    if (authError?.message) {
      description = authError.message;
    }

    if (authError?.status === 400) {
      // Check for specific error messages
      if (authError.message.includes('already registered')) {
        description =
          'This email is already registered. Please sign in instead.';
      } else if (authError.message.includes('password')) {
        description = 'Password should be at least 6 characters long.';
      }
    } else if (authError?.status === 422) {
      description = 'Invalid email format.';
    }

    return {
      type: 'error',
      title: 'Sign Up Failed',
      description,
      data: { error: authError }
    };
  }
}

/**
 * Sign in with email and password
 * @param formData Form data containing email and password
 * @returns AuthResponse with result of the operation
 */
export async function signInWithPassword(
  formData: FormData
): Promise<AuthResponse> {
  try {
    if (!formData) {
      return {
        type: 'error',
        title: 'Sign In Failed',
        description: 'Missing form data'
      };
    }

    const email = String(formData.get('email') || '').trim();
    const password = String(formData.get('password') || '').trim();

    if (!email || !password) {
      return {
        type: 'error',
        title: 'Sign In Failed',
        description: 'Email and password are required'
      };
    }

    const supabase = createClient();

    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password
    });

    if (error) {
      throw error;
    }

    if (!data?.user?.id) {
      return {
        type: 'error',
        title: 'Sign In Failed',
        description: 'User not found'
      };
    }

    // Get user type
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('user_type')
      .eq('id', data.user.id)
      .single();

    if (userError) {
      console.error('Error fetching user data:', userError);
      // Still allow sign in even if we can't get user data
      return {
        type: 'success',
        title: 'Signed In',
        description: 'You are now signed in.',
        redirectPath: '/dashboard'
      };
    }

    // Determine redirect path based on user type
    const userType = userData?.user_type?.toLowerCase();
    let redirectPath = '/dashboard';

    if (userType === 'admin') {
      redirectPath = '/admin';
    } else {
      switch (userType) {
        case 'candidate':
          redirectPath = '/candidate';
          break;
        case 'insider':
          redirectPath = '/insider';
          break;
        case 'company':
          redirectPath = '/company';
          break;
      }
    }

    return {
      type: 'success',
      title: 'Signed In',
      description: 'You are now signed in.',
      redirectPath,
      data: { user: data.user }
    };
  } catch (error) {
    const authError = error as AuthError;
    let description = 'An unexpected error occurred';

    if (authError?.message) {
      description = authError.message;
    }

    // Handle specific auth errors with more user-friendly messages
    if (authError?.status === 400) {
      description = 'Invalid email or password';
    } else if (authError?.status === 422) {
      description = 'Email format is invalid';
    }

    return {
      type: 'error',
      title: 'Sign In Failed',
      description
    };
  }
}

/**
 * Sign out the current user
 * @returns AuthResponse with result of the operation
 */
export async function signOut(): Promise<AuthResponse> {
  try {
    const supabase = createClient();
    const { error } = await supabase.auth.signOut();

    if (error) {
      throw error;
    }

    return {
      type: 'success',
      title: 'Signed Out',
      description: 'You have been successfully signed out.',
      redirectPath: '/'
    };
  } catch (error) {
    const authError = error as AuthError;
    return {
      type: 'error',
      title: 'Sign Out Failed',
      description: authError?.message || 'You could not be signed out.'
    };
  }
}

/**
 * Verify email with OTP
 * @param email User's email address
 * @param otp OTP code
 * @returns AuthResponse with result of the operation
 */
export async function verifyEmail(
  email: string,
  otp: string
): Promise<AuthResponse> {
  try {
    // Input validation
    if (!email || !otp) {
      return {
        type: 'error',
        title: 'Verification Failed',
        description: 'Email and verification code are required'
      };
    }

    const supabase = createClient();

    // Verify OTP
    const { data, error } = await supabase.auth.verifyOtp({
      email,
      token: otp,
      type: 'email'
    });

    if (error) {
      throw error;
    }

    // Get user information after successful verification
    const { data: userData, error: userError } = await supabase.auth.getUser();

    if (userError) {
      console.error('Error fetching user data after verification:', userError);
    }

    // Determine redirect path based on user type (if available)
    let redirectPath = '/dashboard';

    if (userData?.user?.user_metadata?.user_type) {
      const userType = String(
        userData.user.user_metadata.user_type
      ).toLowerCase();

      switch (userType) {
        case 'candidate':
          redirectPath = '/candidate';
          break;
        case 'insider':
          redirectPath = '/insider';
          break;
        case 'company':
          redirectPath = '/company';
          break;
        case 'admin':
          redirectPath = '/admin';
          break;
      }
    }

    return {
      type: 'success',
      title: 'Email Verified',
      description: 'Your email has been verified successfully.',
      redirectPath,
      data: { user: userData?.user }
    };
  } catch (error) {
    const authError = error as AuthError;
    let description = 'An unexpected error occurred';

    if (authError?.message) {
      description = authError.message;
    }

    // Provide user-friendly error messages based on error type
    if (authError?.status === 400 || authError?.status === 401) {
      if (authError.message.includes('expired')) {
        description =
          'Verification code has expired. Please request a new one.';
      } else if (authError.message.includes('invalid')) {
        description = 'Invalid verification code. Please check and try again.';
      }
    }

    return {
      type: 'error',
      title: 'Verification Failed',
      description,
      data: { error: authError }
    };
  }
}

/**
 * Resend verification code for an existing account
 * @param email User's email address
 * @returns AuthResponse with result of the operation
 */
export async function resendVerificationCode(
  email: string
): Promise<AuthResponse> {
  try {
    // Input validation
    if (!email) {
      return {
        type: 'error',
        title: 'Verification Code Request Failed',
        description: 'Email is required'
      };
    }

    const supabase = createClient();

    // Use the correct method to resend verification email
    const { error } = await supabase.auth.resend({
      type: 'signup',
      email,
      options: {
        emailRedirectTo: `${getURL()}/auth/callback?next=/dashboard`
      }
    });

    if (error) {
      throw error;
    }

    return {
      type: 'success',
      title: 'Verification Code Resent',
      description: 'A new verification code has been sent to your email.'
    };
  } catch (error) {
    const authError = error as AuthError;
    let description = authError?.message || 'An unexpected error occurred';

    // Handle specific auth errors with more user-friendly messages
    if (authError?.status === 422) {
      description = 'The email address format is invalid';
    } else if (authError?.status === 429) {
      description = 'Too many requests. Please try again later.';
    }

    return {
      type: 'error',
      title: 'Verification Code Failed',
      description
    };
  }
}

/**
 * Send a password reset email
 * @param email User's email address
 * @param redirectTo URL to redirect to after password reset
 * @returns AuthResponse with result of the operation
 */
export async function resetPasswordForEmail(
  email: string,
  redirectTo: string
): Promise<AuthResponse> {
  if (!email) {
    return {
      type: 'error',
      title: 'Password Reset Failed',
      description: 'Email is required'
    };
  }

  try {
    const supabase = createClient();

    const { error } = await supabase.auth.resetPasswordForEmail(email, {
      redirectTo
    });

    if (error) {
      throw error;
    }

    return {
      type: 'success',
      title: 'Reset Email Sent',
      description: 'Check your email for password reset instructions.'
    };
  } catch (error) {
    const authError = error as AuthError;
    let description = authError?.message || 'An unexpected error occurred';

    // Handle specific auth errors with more user-friendly messages
    if (authError?.status === 422) {
      description = 'The email address format is invalid';
    }

    return {
      type: 'error',
      title: 'Password Reset Failed',
      description
    };
  }
}

/**
 * Update the user's password
 * @param password New password
 * @returns AuthResponse with result of the operation
 */
export async function updatePassword(password: string): Promise<AuthResponse> {
  if (!password) {
    return {
      type: 'error',
      title: 'Password Update Failed',
      description: 'Password is required'
    };
  }

  try {
    const supabase = createClient();

    const { error } = await supabase.auth.updateUser({
      password
    });

    if (error) {
      throw error;
    }

    return {
      type: 'success',
      title: 'Password Updated',
      description: 'Your password has been successfully updated.',
      redirectPath: '/dashboard'
    };
  } catch (error) {
    const authError = error as AuthError;
    let description = authError?.message || 'An unexpected error occurred';

    // Handle specific auth errors with more user-friendly messages
    if (authError?.status === 422) {
      description = 'Password must be at least 6 characters';
    }

    return {
      type: 'error',
      title: 'Password Update Failed',
      description
    };
  }
}

/**
 * Sign in with an OAuth provider
 * @param provider OAuth provider (google, github, etc)
 * @param redirectTo URL to redirect to after sign in
 * @returns AuthResponse with result of the operation
 */
export async function signInWithOAuth(
  provider: Provider,
  redirectTo: string = '/dashboard'
): Promise<AuthResponse> {
  try {
    const supabase = createClient();

    const { error } = await supabase.auth.signInWithOAuth({
      provider,
      options: {
        redirectTo
      }
    });

    if (error) {
      throw error;
    }

    return {
      type: 'success',
      title: 'Redirecting...',
      description: `Signing in with ${provider}`
    };
  } catch (error) {
    const authError = error as AuthError;
    return {
      type: 'error',
      title: 'OAuth Sign-in Failed',
      description: authError?.message || 'Could not sign in with this provider'
    };
  }
}

/**
 * Send a password reset email for migrated users
 * @param email User's email address
 * @returns AuthResponse with result of the operation
 */
export async function sendMigrationPasswordReset(
  email: string
): Promise<AuthResponse> {
  if (!email) {
    return {
      type: 'error',
      title: 'Password Reset Failed',
      description: 'Email is required'
    };
  }

  console.log('🔍 Step 0: Validating email format:', email);
  // Basic email validation to prevent unnecessary API calls
  const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
  if (!emailRegex.test(email)) {
    console.log('❌ Email validation failed');
    return {
      type: 'error',
      title: 'Invalid Email',
      description: 'Please enter a valid email address'
    };
  }

  try {
    const supabase = createClient();
    console.log('🔄 Starting migration password reset process...');

    // Step 1: Check if user exists in database
    console.log('🔍 Step 1: Checking if user exists in database...');
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('id, email, migration_status')
      .eq('email', email)
      .single();

    if (userError || !userData) {
      console.log('❌ User not found in database:', userError);
      return {
        type: 'error',
        title: 'Password Reset Failed',
        description: 'User not found in the system.'
      };
    }
    console.log('✅ User found in database:', {
      id: userData.id,
      email: userData.email,
      status: userData.migration_status
    });

    // Step 2: Try password reset to check if user exists in auth
    console.log('🔍 Step 2: Checking if user exists in auth system...');
    try {
      const { error: resetError } = await supabase.auth.resetPasswordForEmail(
        email,
        {
          redirectTo: `${getURL()}/auth/callback?next=/dashboard`
        }
      );

      // If no error, user exists and password reset email sent
      if (!resetError) {
        console.log('✅ User exists in auth system, password reset email sent');
        await supabase
          .from('users')
          .update({ migration_status: 'password_reset_sent' })
          .eq('email', email);

        return {
          type: 'success',
          title: 'Password Reset Email Sent',
          description:
            'Please check your email (including spam folder) for a link to reset your password.',
          redirectPath: '/signin'
        };
      }

      console.log('ℹ️ Reset password attempt result:', resetError?.message);
      // If error is not "user not found", throw it
      if (!resetError.message?.includes('User not found')) {
        throw resetError;
      }
      console.log(
        'ℹ️ User not found in auth system, will create new auth user'
      );
    } catch (error) {
      const authError = error as AuthError;
      // If error is not "user not found", throw it
      if (!authError.message?.includes('User not found')) {
        throw error;
      }
      console.log(
        'ℹ️ Confirmed user not in auth system, proceeding to creation'
      );
    }

    // Step 3: Create user in auth system
    console.log('🔍 Step 3: Creating new user in auth system...');
    const tempPassword = Math.random().toString(36).slice(-12);
    const { data: signUpData, error: signUpError } = await supabase.auth.signUp(
      {
        email,
        password: tempPassword,
        options: {
          emailRedirectTo: `${getURL()}/auth/callback?next=/dashboard`,
          data: {
            id: userData.id
          }
        }
      }
    );

    if (signUpError) {
      console.log('❌ Error during user creation:', signUpError.message);
      // If user already exists, proceed with password reset
      if (!signUpError.message?.includes('already registered')) {
        throw signUpError;
      }
      console.log(
        'ℹ️ User already exists in auth, proceeding with password reset'
      );
    } else {
      console.log('✅ New auth user created successfully');
    }

    // Step 4: Send password reset email
    console.log('🔍 Step 4: Sending final password reset email...');
    const { error: finalResetError } =
      await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${getURL()}/auth/callback?next=/dashboard`
      });

    if (finalResetError) {
      console.log(
        '❌ Error sending final reset email:',
        finalResetError.message
      );
      throw finalResetError;
    }
    console.log('✅ Password reset email sent successfully');

    // Update migration status
    console.log('🔍 Step 5: Updating migration status...');
    await supabase
      .from('users')
      .update({ migration_status: 'password_reset_sent' })
      .eq('email', email);
    console.log('✅ Migration status updated');

    return {
      type: 'success',
      title: 'Password Reset Email Sent',
      description:
        'Please check your email (including spam folder) for a link to reset your password.',
      redirectPath: '/signin'
    };
  } catch (error) {
    const authError = error as AuthError;
    console.error('❌ Error in migration password reset:', {
      error,
      message: authError?.message,
      status: authError?.status
    });

    // If user already exists, treat as success
    if (authError.message?.includes('already registered')) {
      return {
        type: 'success',
        title: 'Password Reset Email Sent',
        description:
          'Please check your email (including spam folder) for a link to reset your password.',
        redirectPath: '/signin'
      };
    }

    let description = 'Unable to process your request. Please try again later.';
    if (authError.message?.toLowerCase().includes('invalid')) {
      description = 'Please check that your email address is correct';
    } else if (authError.message?.includes('User not found')) {
      description = 'User not found in the system';
    }

    return {
      type: 'error',
      title: 'Password Reset Failed',
      description
    };
  }
}
