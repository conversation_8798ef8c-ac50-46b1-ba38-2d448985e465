import { BUBBLE_CONFIG } from "@/config/bubble-config";
import { getTokens } from "./queries";

class BubbleClient {
  public baseUrl: string;
  public apiKey: string;
  private version: string;

  constructor() {
    this.baseUrl = BUBBLE_CONFIG.API_URL;
    this.apiKey = BUBBLE_CONFIG.API_KEY;
    this.version = BUBBLE_CONFIG.APP_VERSION;
  }

  public async request(endpoint: string, options: RequestInit = {}) {
    const url = `${this.baseUrl}/api/1.1${endpoint}`;
    const headers = {
      'Authorization': `Bearer ${this.apiKey}`,
      'Content-Type': 'application/json',
      ...options.headers
    };

    const response = await fetch(url, { ...options, headers });
    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.message || 'An error occurred');
    }

    return data;
  }

  public async dataRequest(endpoint: string, options: RequestInit = {}) {
    const { token } = getTokens();
    const url = `${this.baseUrl}/api/1.1${endpoint}`;
    const headers = {
      'Authorization': `Bearer ${token || this.apiKey}`,
      'Content-Type': 'application/json',
      ...options.headers
    };

    const response = await fetch(url, { ...options, headers });
    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.message || 'An error occurred');
    }

    return data;
  }

  public async userRequest(endpoint: string, options: RequestInit = {}): Promise<any> {
    const url = `${this.baseUrl}/api/1.1${endpoint}`;
    const { token } = getTokens();
    if(!token) {
      throw new Error('No token found');
    }
    const headers = {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json',
      ...options.headers
    };

    const response = await fetch(url, { ...options, headers });
    const data = await response.json();
    console.log("This is the data", data)

    if (!response.ok) {
      throw new Error(data.message || 'An error occurred');
    }

    return data;
  }

  async signIn(email: string, password: string) {
    return this.request('/wf/login', {
      method: 'POST',
      body: JSON.stringify({ email, password })
    });
  }

  async signUp(email: string, password: string, user_type: string) {
    return this.request('/wf/signup', {
      method: 'POST',
      body: JSON.stringify({ email, password, user_type })
    });
  }

  async getCurrentUser(): Promise<any> {
    const { token, user_id } = getTokens();
    
    if (!token || !user_id) {
      return null;
    }

    try {
      const response = await this.dataRequest(`/obj/User/${user_id}`, {
        method: 'GET'
      });
      
      return response.response || null;
    } catch (error) {
      console.error('Error getting current user:', error);
      return null;
    }
  }

  async isAuthenticated(): Promise<boolean> {
    const { token } = getTokens();
    if (!token) return false;

    try {
      const user = await this.getCurrentUser();
      return !!user;
    } catch {
      return false;
    }
  }

  
  async sendEmail(email: string, subject: string, body: string) {
    console.log('[BubbleClient] Attempting to send email to:', email);
    
    try {
      // Use the correct endpoint - sendemail (not sendmail)
      // And use the standard request method that works for other workflows
      const result = await this.request('/wf/sendmail', {
        method: 'POST',
        body: JSON.stringify({ email, subject, body })
      });
      
      console.log('[BubbleClient] Email sent successfully');
      return result;
    } catch (error) {
      console.error('[BubbleClient] Failed to send email:', error);
      throw error;
    }
  }

  async forgotPassword(email: string): Promise<any> {
    return this.request('/wf/forgot-password', {
      method: 'POST',
      body: JSON.stringify({ email })
    });
  }

  async resetPassword(email: string, oldPassword: string, newPassword: string): Promise<any> {
    return this.userRequest('/wf/reset-password', {
      method: 'POST',
      body: JSON.stringify({ email, oldPassword, newPassword })
    });
  }
}

export const bubbleClient = new BubbleClient();