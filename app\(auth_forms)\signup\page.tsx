'use client';

import Link from 'next/link';
import Image from 'next/image';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import {
  signUp,
  verifyEmail,
  signUpWithOAuth,
  resendVerificationCode
} from '@/utils/bubble/auth';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { useToast } from '@/components/ui/use-toast';
import { Chrome, ArrowLeft, Linkedin } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription
} from '@/components/ui/dialog';
import type React from 'react';
import { Meteors } from '@/components/magicui/meteors';
import Particles from '@/components/magicui/particles';

export default function SignUp() {
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { toast } = useToast();
  const [showOTPDialog, setShowOTPDialog] = useState(false);
  const [otp, setOtp] = useState('');
  const [email, setEmail] = useState('');

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      const formData = new FormData(e.currentTarget);
      formData.append('userType', 'Candidate'); // Default user type is now Candidate

      const emailValue = formData.get('email') as string;
      setEmail(emailValue);

      const result = await signUp(formData);

      if (result.type === 'success') {
        if (result.showOTP) {
          setShowOTPDialog(true);
          toast({
            title: result.title,
            description: result.description,
            duration: 5000
          });
        } else {
          router.push('/'); // Default to home page since redirectPath doesn't exist
        }
      } else {
        toast({
          variant: 'destructive',
          title: result.title,
          description: result.description,
          duration: 5000
        });
      }
    } catch (error) {
      toast({
        variant: 'destructive',
        title: 'Error',
        description: 'Something went wrong. Please try again.',
        duration: 5000
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleOTPSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      const result = await verifyEmail(email, otp);

      if (result.type === 'success') {
        toast({
          title: result.title,
          description: result.description,
          duration: 5000
        });
        setShowOTPDialog(false);
        router.push(result?.redirectPath || '');
      } else {
        toast({
          variant: 'destructive',
          title: result.title,
          description: result.description,
          duration: 5000
        });
      }
    } catch (error) {
      toast({
        variant: 'destructive',
        title: 'Error',
        description: 'Failed to verify OTP. Please try again.',
        duration: 5000
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleResendOTP = async () => {
    setIsSubmitting(true);
    try {
      // Use the new resendVerificationCode function instead of signUp
      // since the account is already created
      const result = await resendVerificationCode(email, 'Candidate');

      if (result.type === 'success') {
        toast({
          title: 'OTP Resent',
          description: 'A new verification code has been sent to your email.',
          duration: 5000
        });
      } else {
        toast({
          variant: 'destructive',
          title: result.title || 'Failed to Resend',
          description:
            result.description ||
            'Could not send new verification code. Please try again.',
          duration: 5000
        });
      }
    } catch (error) {
      toast({
        variant: 'destructive',
        title: 'Error',
        description: 'Failed to resend OTP. Please try again.',
        duration: 5000
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleOAuthSignUp = async (provider: string) => {
    setIsSubmitting(true);
    try {
      const result = await signUpWithOAuth(provider, 'candidate');

      if (result.type === 'success') {
        toast({
          title: result.title,
          description: result.description,
          duration: 5000
        });
      } else {
        toast({
          variant: 'destructive',
          title: result.title,
          description: result.description,
          duration: 5000
        });
      }
    } catch (error) {
      toast({
        variant: 'destructive',
        title: 'Error',
        description: 'Failed to sign up with OAuth. Please try again.',
        duration: 5000
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <>
      <div className="flex min-h-[100dvh] bg-background">
        {/* Left Section with Illustration */}
        <div className="hidden lg:flex flex-col flex-1 bg-[#118073] text-white p-12 justify-between relative overflow-hidden">
          <div className="relative z-10">
            <div className="w-40 h-auto mb-8">
              <Image
                src="/footer_logo.png"
                alt="InternUp Logo"
                width={160}
                height={40}
                className="object-contain"
              />
            </div>
            <div className="space-y-6 max-w-md">
              <h1 className="text-4xl font-bold">Start Your Journey</h1>
              <p className="text-xl">
                Create an account to get started with InternUp - connecting
                talent with opportunities.
              </p>
            </div>
          </div>

          <div className="relative z-10">
            <p className="text-sm opacity-80">
              {new Date().getFullYear()} InternUp. All rights reserved.
            </p>
          </div>

          {/* Decorative elements */}
          <div className="absolute bottom-0 right-0 w-64 h-64 bg-[#16a38a] rounded-full filter blur-3xl opacity-20 -mr-20 -mb-20"></div>
          <div className="absolute top-0 left-0 w-96 h-96 bg-[#4ade80] rounded-full filter blur-3xl opacity-10 -ml-40 -mt-40"></div>
          <Particles
            className="absolute inset-0"
            quantity={300}
            size={1}
            ease={1}
            refresh
          />
        </div>

        {/* Right Section with Form */}
        <div className="flex-1 px-4 py-12 sm:px-6 lg:px-8 overflow-y-auto">
          <div className="flex items-center justify-between mb-8">
            <Link
              href="/"
              className="rounded-md p-2 transition-colors hover:bg-muted"
              prefetch={false}
            >
              <ArrowLeft className="h-5 w-5" />
              <span className="sr-only">Back</span>
            </Link>
          </div>

          <div className="max-w-md mx-auto">
            <div className="space-y-6 text-center mb-8">
              <h1 className="text-3xl font-bold bg-[#36BA98] bg-clip-text text-transparent">
                Create an Account
              </h1>
              <p className="text-muted-foreground">
                Join our community and discover opportunities
              </p>
            </div>

            <form className="space-y-6" onSubmit={handleSubmit}>
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="email" className="text-sm font-medium">
                    Email
                  </Label>
                  <Input
                    id="email"
                    type="email"
                    name="email"
                    placeholder="<EMAIL>"
                    autoCapitalize="none"
                    autoComplete="email"
                    autoCorrect="off"
                    required
                    className="border-muted-foreground/20 focus-visible:ring-[#118073]"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="password" className="text-sm font-medium">
                    Password
                  </Label>
                  <Input
                    id="password"
                    type="password"
                    name="password"
                    required
                    className="border-muted-foreground/20 focus-visible:ring-[#118073]"
                  />
                  <div className="space-y-1 text-sm text-muted-foreground mt-2">
                    <p>Your password should have:</p>
                    <ul className="space-y-1 ml-5 list-disc">
                      <li className="text-[#4ade80]">At least 6 characters</li>
                      <li className="text-[#4ade80]">At least one number</li>
                      <li className="text-[#4ade80]">
                        At least one capital letter
                      </li>
                    </ul>
                  </div>
                </div>
                <div className="flex items-center space-x-2 pt-2">
                  <Checkbox
                    id="terms"
                    name="terms"
                    required
                    className="border-muted-foreground/30 data-[state=checked]:bg-[#118073] data-[state=checked]:border-[#118073]"
                  />
                  <Label htmlFor="terms" className="text-sm">
                    I agree to the{' '}
                    <Link href="#" className="text-[#118073] hover:underline">
                      terms of service
                    </Link>
                  </Label>
                </div>
              </div>

              <Button
                type="submit"
                className="w-full bg-[#118073] hover:bg-[#16a38a] text-white transition-colors mt-6"
                size="lg"
                disabled={isSubmitting}
              >
                Sign up
              </Button>

              <div className="relative my-8">
                <div className="relative flex justify-center text-xs uppercase">
                  <span className="bg-background px-2 text-muted-foreground">
                    Or continue with
                  </span>
                </div>
              </div>

              <div className="grid gap-3">
                <Button
                  variant="outline"
                  className="w-full border-muted-foreground/20 hover:bg-[#e6f7ef] hover:text-[#118073] transition-colors"
                  disabled={isSubmitting}
                  onClick={() => handleOAuthSignUp('google')}
                  type="button"
                >
                  <Chrome className="mr-2 h-4 w-4" />
                  Sign up with Google
                </Button>
                <Button
                  variant="outline"
                  className="w-full border-muted-foreground/20 hover:bg-[#e6f7ef] hover:text-[#118073] transition-colors"
                  disabled={isSubmitting}
                  onClick={() => handleOAuthSignUp('linkedin_oidc')}
                  type="button"
                >
                  <Linkedin className="mr-2 h-4 w-4" />
                  Sign up with LinkedIn
                </Button>
              </div>

              <div className="text-center mt-8">
                <Link
                  href="/signin"
                  className="text-[#118073] hover:text-[#16a38a] text-sm font-medium hover:underline transition-colors"
                  prefetch={false}
                >
                  Already have an account?{' '}
                  <span className="font-semibold">Sign in</span>
                </Link>
              </div>
            </form>
          </div>
        </div>
      </div>

      {/* OTP Verification Dialog */}
      <Dialog open={showOTPDialog} onOpenChange={setShowOTPDialog}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="text-xl font-semibold text-center">
              Enter Verification Code
            </DialogTitle>
            <DialogDescription className="text-center">
              We've sent a verification code to your email address. Please enter
              it below.
            </DialogDescription>
          </DialogHeader>

          <form onSubmit={handleOTPSubmit} className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="otp" className="text-sm font-medium">
                Verification Code
              </Label>
              <Input
                id="otp"
                type="text"
                value={otp}
                onChange={(e) => setOtp(e.target.value)}
                placeholder="Enter your verification code"
                className="border-muted-foreground/20 focus-visible:ring-[#118073]"
                required
              />
            </div>

            <div className="flex justify-between items-center mt-4">
              <Button
                type="button"
                variant="ghost"
                onClick={handleResendOTP}
                disabled={isSubmitting}
                className="text-[#118073] hover:text-[#16a38a] hover:bg-[#e6f7ef]"
              >
                Resend Code
              </Button>

              <Button
                type="submit"
                disabled={isSubmitting}
                className="bg-[#118073] hover:bg-[#16a38a] text-white transition-colors"
              >
                Verify
              </Button>
            </div>
          </form>
        </DialogContent>
      </Dialog>
    </>
  );
}
