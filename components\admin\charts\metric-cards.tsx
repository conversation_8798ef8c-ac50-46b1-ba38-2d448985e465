'use client';

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import {
  Briefcase,
  FileText,
  Building,
  Users
} from 'lucide-react';
import { DashboardMetrics } from '@/actions/admin/dashboard';

interface MetricCardProps {
  title: string;
  value: number;
  icon: React.ReactNode;
  description?: string;
  breakdown?: string;
}

interface MetricCardsProps {
  metrics: DashboardMetrics;
}

function MetricCard({ title, value, icon, description, breakdown }: MetricCardProps) {
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
        <div className="h-4 w-4 text-muted-foreground">
          {icon}
        </div>
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">{value.toLocaleString()}</div>
        {breakdown && (
          <p className="text-xs text-muted-foreground mt-1">{breakdown}</p>
        )}
        {description && (
          <p className="text-xs text-muted-foreground mt-1">{description}</p>
        )}
      </CardContent>
    </Card>
  );
}

export function MetricCards({ metrics }: MetricCardsProps) {
  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      <MetricCard
        title="Total Jobs"
        value={metrics.totalJobs}
        icon={<Briefcase className="h-4 w-4" />}
        breakdown={`${metrics.startupJobs} startup, ${metrics.publicJobs} public`}
        description="Active job listings across all platforms"
      />
      <MetricCard
        title="Total Applications"
        value={metrics.totalApplications}
        icon={<FileText className="h-4 w-4" />}
        breakdown={`${metrics.startupApplications} startup, ${metrics.publicApplications} public`}
        description="Applications submitted by candidates"
      />
      <MetricCard
        title="Total Companies"
        value={metrics.totalCompanies}
        icon={<Building className="h-4 w-4" />}
        breakdown={`${metrics.startupCompanies} startup, ${metrics.publicCompanies} public`}
        description="Registered companies on the platform"
      />
      <MetricCard
        title="Total Users"
        value={metrics.totalUsers}
        icon={<Users className="h-4 w-4" />}
        description="Active users on the platform"
      />
    </div>
  );
}
