'use client';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { 
  Briefcase, 
  FileText, 
  Building, 
  Users,
  Clock
} from 'lucide-react';
import { RecentActivity } from '@/actions/admin/dashboard';
import { formatDistanceToNow } from 'date-fns';

interface RecentActivityProps {
  activities: RecentActivity[];
}

function getActivityIcon(type: RecentActivity['type']) {
  switch (type) {
    case 'job':
      return <Briefcase className="h-4 w-4" />;
    case 'application':
      return <FileText className="h-4 w-4" />;
    case 'company':
      return <Building className="h-4 w-4" />;
    case 'user':
      return <Users className="h-4 w-4" />;
    default:
      return <Clock className="h-4 w-4" />;
  }
}

function getActivityColor(type: RecentActivity['type']) {
  switch (type) {
    case 'job':
      return 'bg-blue-500';
    case 'application':
      return 'bg-green-500';
    case 'company':
      return 'bg-purple-500';
    case 'user':
      return 'bg-orange-500';
    default:
      return 'bg-gray-500';
  }
}

function getStatusBadge(status?: string) {
  if (!status) return null;

  const statusLower = status.toLowerCase();
  let variant: "default" | "secondary" | "destructive" | "outline" = "default";
  
  if (statusLower.includes('pending') || statusLower.includes('submitted')) {
    variant = "secondary";
  } else if (statusLower.includes('approved') || statusLower.includes('accepted')) {
    variant = "default";
  } else if (statusLower.includes('rejected') || statusLower.includes('declined')) {
    variant = "destructive";
  }

  return <Badge variant={variant} className="text-xs">{status}</Badge>;
}

export function RecentActivityCard({ activities }: RecentActivityProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Recent Activity</CardTitle>
        <CardDescription>
          Latest updates across the platform
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {activities.length === 0 ? (
            <div className="text-center text-muted-foreground py-8">
              <Clock className="h-8 w-8 mx-auto mb-2 opacity-50" />
              <p>No recent activity</p>
            </div>
          ) : (
            activities.map((activity) => (
              <div key={activity.id} className="flex items-start space-x-3">
                <Avatar className="h-8 w-8">
                  <AvatarFallback className={`${getActivityColor(activity.type)} text-white`}>
                    {getActivityIcon(activity.type)}
                  </AvatarFallback>
                </Avatar>
                <div className="flex-1 space-y-1">
                  <div className="flex items-center justify-between">
                    <p className="text-sm font-medium leading-none">
                      {activity.title}
                    </p>
                    {activity.status && getStatusBadge(activity.status)}
                  </div>
                  <p className="text-sm text-muted-foreground">
                    {activity.description}
                  </p>
                  <p className="text-xs text-muted-foreground">
                    {formatDistanceToNow(new Date(activity.timestamp), { addSuffix: true })}
                  </p>
                </div>
              </div>
            ))
          )}
        </div>
      </CardContent>
    </Card>
  );
}

// Quick stats component for the activity section
export function QuickStats() {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Quick Stats</CardTitle>
        <CardDescription>
          Key metrics at a glance
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <div className="h-2 w-2 bg-blue-500 rounded-full"></div>
              <span className="text-sm text-muted-foreground">Jobs Posted Today</span>
            </div>
            <span className="text-sm font-medium">24</span>
          </div>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <div className="h-2 w-2 bg-green-500 rounded-full"></div>
              <span className="text-sm text-muted-foreground">Applications Today</span>
            </div>
            <span className="text-sm font-medium">156</span>
          </div>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <div className="h-2 w-2 bg-purple-500 rounded-full"></div>
              <span className="text-sm text-muted-foreground">New Companies</span>
            </div>
            <span className="text-sm font-medium">8</span>
          </div>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <div className="h-2 w-2 bg-orange-500 rounded-full"></div>
              <span className="text-sm text-muted-foreground">Active Users</span>
            </div>
            <span className="text-sm font-medium">1,247</span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
